using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

/// <summary>
/// Enhanced VIX service that uses Alpaca's Algo Trader Plus subscription
/// to get VIX-equivalent data from VIX proxy ETFs
/// </summary>
public interface IAlpacaVixService
{
    Task<decimal> GetCurrentVixAsync();
    Task<decimal> GetVixChangeAsync(TimeSpan period);
    Task<VixAnalysis> GetVixAnalysisAsync();
    Task<bool> TestConnectionAsync();
}

public class AlpacaVixService : IAlpacaVixService
{
    private readonly ILogger<AlpacaVixService> _logger;
    private readonly IAlpacaService _alpacaService;
    private readonly IPolygonDataService _polygonDataService;
    private readonly IConfiguration _configuration;
    
    // Cache for VIX data
    private decimal _cachedVix = 0;
    private DateTime _lastVixUpdate = DateTime.MinValue;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(1);
    
    // VIX proxy ETF weights and conversion factors
    private readonly Dictionary<string, VixProxyConfig> _vixProxies = new()
    {
        { "VXX", new VixProxyConfig { Weight = 1.0m, ConversionFactor = 0.8m, Description = "iPath S&P 500 VIX Short-Term Futures ETN" } },
        { "VIXY", new VixProxyConfig { Weight = 0.8m, ConversionFactor = 1.5m, Description = "ProShares VIX Short-Term Futures ETF" } },
        { "UVXY", new VixProxyConfig { Weight = 0.6m, ConversionFactor = 2.5m, Description = "ProShares Ultra VIX Short-Term Futures ETF (2x)" } },
        { "SVXY", new VixProxyConfig { Weight = 0.4m, ConversionFactor = -1.0m, Description = "ProShares Short VIX Short-Term Futures ETF (inverse)" } }
    };

    public AlpacaVixService(
        ILogger<AlpacaVixService> logger,
        IAlpacaService alpacaService,
        IPolygonDataService polygonDataService,
        IConfiguration configuration)
    {
        _logger = logger;
        _alpacaService = alpacaService;
        _polygonDataService = polygonDataService;
        _configuration = configuration;
    }

    public async Task<decimal> GetCurrentVixAsync()
    {
        try
        {
            // Check cache first
            if (_cachedVix > 0 && DateTime.UtcNow - _lastVixUpdate < _cacheExpiry)
            {
                _logger.LogDebug($"Returning cached VIX: {_cachedVix:F2}");
                return _cachedVix;
            }

            _logger.LogDebug("Calculating VIX using Alpaca Algo Trader Plus data");

            // Try to get VIX from multiple proxy ETFs
            var vixCalculations = new List<VixCalculation>();

            foreach (var proxy in _vixProxies)
            {
                try
                {
                    var price = await _alpacaService.GetCurrentPriceAsync(proxy.Key);
                    if (price > 0)
                    {
                        var vixEquivalent = CalculateVixFromProxy(price, proxy.Value);
                        vixCalculations.Add(new VixCalculation
                        {
                            Symbol = proxy.Key,
                            Price = price,
                            VixEquivalent = vixEquivalent,
                            Weight = proxy.Value.Weight,
                            Confidence = GetConfidenceScore(proxy.Key, price)
                        });

                        _logger.LogDebug($"VIX from {proxy.Key}: ${price:F2} -> VIX {vixEquivalent:F2} (weight: {proxy.Value.Weight:F1})");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, $"Failed to get price for VIX proxy {proxy.Key}");
                }
            }

            // Calculate weighted average VIX
            if (vixCalculations.Any())
            {
                var weightedVix = CalculateWeightedVix(vixCalculations);
                _cachedVix = weightedVix;
                _lastVixUpdate = DateTime.UtcNow;
                
                _logger.LogInformation($"Calculated VIX from {vixCalculations.Count} proxies: {weightedVix:F2}");
                return weightedVix;
            }

            // Fallback to Polygon if available
            if (_polygonDataService != null)
            {
                try
                {
                    var polygonVix = await _polygonDataService.GetCurrentVixAsync();
                    if (polygonVix > 0)
                    {
                        _logger.LogInformation($"Using Polygon VIX fallback: {polygonVix:F2}");
                        _cachedVix = polygonVix;
                        _lastVixUpdate = DateTime.UtcNow;
                        return polygonVix;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Polygon VIX fallback failed");
                }
            }

            // Final fallback
            _logger.LogWarning("All VIX sources failed, using conservative fallback");
            return 20.0m;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current VIX");
            return 20.0m;
        }
    }

    public async Task<decimal> GetVixChangeAsync(TimeSpan period)
    {
        try
        {
            var currentVix = await GetCurrentVixAsync();
            
            // For now, return 0 change as we don't have historical proxy data
            // This could be enhanced to store historical VIX calculations
            _logger.LogDebug($"VIX change calculation not yet implemented for period {period}");
            return 0m;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating VIX change for period {period}");
            return 0m;
        }
    }

    public async Task<VixAnalysis> GetVixAnalysisAsync()
    {
        try
        {
            var currentVix = await GetCurrentVixAsync();
            
            return new VixAnalysis
            {
                CurrentLevel = currentVix,
                Interpretation = GetVixInterpretation(currentVix),
                RiskLevel = GetRiskLevel(currentVix),
                TradingRecommendation = GetTradingRecommendation(currentVix),
                PositionSizeMultiplier = GetPositionSizeMultiplier(currentVix),
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing VIX analysis");
            return new VixAnalysis
            {
                CurrentLevel = 20.0m,
                Interpretation = "Error - Using Conservative Default",
                RiskLevel = "Medium",
                TradingRecommendation = "Proceed with caution",
                PositionSizeMultiplier = 0.8m,
                Timestamp = DateTime.UtcNow
            };
        }
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            _logger.LogInformation("Testing Alpaca VIX service connection...");
            
            // Test by trying to get VXX price
            var vxxPrice = await _alpacaService.GetCurrentPriceAsync("VXX");
            if (vxxPrice > 0)
            {
                _logger.LogInformation($"Alpaca VIX service test successful - VXX: ${vxxPrice:F2}");
                return true;
            }
            
            _logger.LogWarning("Alpaca VIX service test failed - no VXX data");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Alpaca VIX service connection test failed");
            return false;
        }
    }

    private decimal CalculateVixFromProxy(decimal price, VixProxyConfig config)
    {
        if (config.ConversionFactor < 0)
        {
            // Inverse ETF (like SVXY)
            return Math.Max(10m, 30m - (price * Math.Abs(config.ConversionFactor)));
        }
        
        return Math.Max(10m, Math.Min(80m, price * config.ConversionFactor));
    }

    private decimal CalculateWeightedVix(List<VixCalculation> calculations)
    {
        var totalWeight = calculations.Sum(c => c.Weight * c.Confidence);
        var weightedSum = calculations.Sum(c => c.VixEquivalent * c.Weight * c.Confidence);
        
        return totalWeight > 0 ? weightedSum / totalWeight : 20.0m;
    }

    private decimal GetConfidenceScore(string symbol, decimal price)
    {
        // Higher confidence for more liquid ETFs and reasonable price ranges
        return symbol switch
        {
            "VXX" when price > 10 && price < 100 => 1.0m,
            "VIXY" when price > 5 && price < 50 => 0.9m,
            "UVXY" when price > 3 && price < 30 => 0.8m,
            "SVXY" when price > 10 && price < 200 => 0.7m,
            _ => 0.5m
        };
    }

    private string GetVixInterpretation(decimal vix)
    {
        return vix switch
        {
            < 12 => "Very Low (Complacency Risk)",
            < 15 => "Low (Good for Premium Selling)",
            < 20 => "Normal (Standard Trading)",
            < 25 => "Elevated (Caution Advised)",
            < 30 => "High (Defensive Strategies)",
            < 35 => "Very High (Minimal Trading)",
            _ => "Extreme (Emergency Mode)"
        };
    }

    private string GetRiskLevel(decimal vix)
    {
        return vix switch
        {
            < 15 => "Low",
            < 25 => "Medium",
            < 35 => "High",
            _ => "Extreme"
        };
    }

    private string GetTradingRecommendation(decimal vix)
    {
        return vix switch
        {
            < 12 => "Aggressive premium selling, watch for volatility expansion",
            < 15 => "Good environment for credit spreads and iron condors",
            < 20 => "Standard 0 DTE strategies, normal position sizing",
            < 25 => "Reduce position sizes, focus on high probability trades",
            < 30 => "Defensive mode, minimal new positions",
            < 35 => "Emergency protocols, close risky positions",
            _ => "Market crisis mode, preserve capital"
        };
    }

    private decimal GetPositionSizeMultiplier(decimal vix)
    {
        return vix switch
        {
            < 12 => 1.5m,   // Increase size in low vol
            < 15 => 1.3m,
            < 18 => 1.1m,
            < 22 => 1.0m,   // Normal sizing
            < 25 => 0.8m,   // Reduce size
            < 30 => 0.6m,
            < 35 => 0.4m,
            _ => 0.2m       // Minimal sizing in crisis
        };
    }
}

// Supporting classes
public class VixProxyConfig
{
    public decimal Weight { get; set; }
    public decimal ConversionFactor { get; set; }
    public string Description { get; set; } = string.Empty;
}

public class VixCalculation
{
    public string Symbol { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public decimal VixEquivalent { get; set; }
    public decimal Weight { get; set; }
    public decimal Confidence { get; set; }
}

public class VixAnalysis
{
    public decimal CurrentLevel { get; set; }
    public string Interpretation { get; set; } = string.Empty;
    public string RiskLevel { get; set; } = string.Empty;
    public string TradingRecommendation { get; set; } = string.Empty;
    public decimal PositionSizeMultiplier { get; set; }
    public DateTime Timestamp { get; set; }
}
